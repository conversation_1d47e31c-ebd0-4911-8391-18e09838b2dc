import {
  CharacteristicsSpreadsheetCell,
  ISpecificConsumptionTableWithSettingsResponse,
} from 'entities/api/calcModelWerManager.entities'
import Handsontable from 'handsontable'
import { EditorType } from 'handsontable/editors'
import { RootStore } from 'stores/RootStore'
import { SpreadsheetBaseProps } from 'widgets/Spreadsheet/ui/lib'

type GridSettings = Handsontable.GridSettings

interface SpecificConsumptionSpreadsheetColumnProps {
  readonly: boolean
  editor: EditorType | boolean
}

interface CharacteristicsSpreadsheetCellProps extends CharacteristicsSpreadsheetCell {
  className: string
  message?: string
}

type SpecificConsumptionSpreadsheetProps = SpreadsheetBaseProps<
  SpecificConsumptionSpreadsheetColumnProps,
  CharacteristicsSpreadsheetCellProps
>

export interface IWerSpecificConsumptionStore {
  rootStore: RootStore
  initialSettings: ISpecificConsumptionTableWithSettingsResponse['settings'] | null
  currentSettings: ISpecificConsumptionTableWithSettingsResponse['settings'] | null
  isSettingsModified: boolean
  isSettingsSaving: boolean
  isCharacteristicsLoaded: boolean
  lastFilledRow: ISpecificConsumptionTableWithSettingsResponse['lastFilledRow'] | null
  specificConsumptionSpreadsheetData: SpecificConsumptionSpreadsheetProps
  originalSpecificConsumptionSpreadsheetData: SpecificConsumptionSpreadsheetProps
  isEditRows: boolean
  actualData: number[][]
  isActualDataLoading: boolean

  resetStore: () => void
  getSpecificConsumptionCharacteristics: (plantId: number, date: string) => Promise<void>
  saveSpecificConsumptionSettings: (
    plantId: number,
    date: string,
    settings: ISpecificConsumptionTableWithSettingsResponse['settings'],
  ) => Promise<void>
  updateCharacteristicsSpreadsheetData: GridSettings['afterChange']
  saveChangedCharacteristicsSpreadsheetData: () => Promise<void>
  resetCharacteristicsSpreadsheetData: () => void
  resetSettings: () => void
  getActualData: (
    plantId: number,
    date: string,
    startDate: string,
    endDate: string,
    signal?: AbortSignal,
  ) => Promise<void>
  setRowCount: (rowCount: number | string) => void
  setSource: (source: ISpecificConsumptionTableWithSettingsResponse['settings']['source']) => void
  setUnit: (unit: ISpecificConsumptionTableWithSettingsResponse['settings']['unit']) => void
  setPressureAllowableChange: (pressureAllowableChange: number | string) => void
  setConsumptionAllowableChange: (consumptionAllowableChange: number | string) => void
  setEmptyValuesValidation: (emptyValuesValidation: boolean) => void
  setAllowableChangeValidation: (allowableChangeValidation: boolean) => void
  setMonotonyValidation: (monotonyValidation: boolean) => void
  setPolynom: (index: number, value: number | string) => void
  setMethod: (method: ISpecificConsumptionTableWithSettingsResponse['settings']['method']) => void
}
