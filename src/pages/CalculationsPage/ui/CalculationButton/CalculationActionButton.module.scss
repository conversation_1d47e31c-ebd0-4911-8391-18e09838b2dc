.ButtonContainer {
  height: 26px;

  > div {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}

.TooltipMessage {
  white-space: pre-line;
}

.PROGRESS_UPDATE {
  pointer-events: auto !important;

  & svg {
    color: var(--blue-color) !important;
    animation: spin 1.5s linear 0s infinite;
  }
}
.NOT_EXECUTED_UPDATE {
  color: var(--text-gray) !important;
}
.DONE_UPDATE {
  color: var(--green-color) !important;
}
.ERROR_UPDATE {
  color: var(--red-color) !important;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ButtonIcon {
  width: 20px !important;
  height: 20px !important;
  max-width: 20px !important;
  max-height: 20px !important;
  min-width: 20px !important;
  min-height: 20px !important;
  padding: 0 !important;
  margin: 0 6px !important;
}

.DisabledButton {
  filter: grayscale(1);
}

.ButtonHistoryForRegular {
  color: var(--text-gray) !important;
  @extend .ButtonIcon;

  margin: 0 !important;
}

.ButtonReset {
  position: relative;
  top: -1px;
  width: 94px;
  height: 26px;
  font-size: 15px !important;
}

.ButtonInit {
  color: var(--blue-color) !important;
  @extend .ButtonIcon;

  margin: 0 !important;
}

.ButtonInitNeedInit {
  color: var(--red-color) !important;
  @extend .ButtonIcon;

  margin: 0 !important;
}

.ButtonUpdateVault {
  @extend .ButtonIcon;

  margin: 0 !important;
}

.LoaderContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}
