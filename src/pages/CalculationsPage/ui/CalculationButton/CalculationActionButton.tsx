import { ButtonProps } from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import { ICalcPossibility, TStatusUpdateModalVault } from 'entities/api/calculationsManager.entities.ts'
import { TaskStatus } from 'entities/shared/common.entities.ts'
import { ICalculationsPageStore } from 'entities/store/calculationPageStore.entities.ts'
import { MessagesWarnings } from 'entities/widgets/Vault.entities.ts'
import { observer } from 'mobx-react'
import { FC, ReactNode, useMemo } from 'react'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { Button } from 'shared/ui/Button'
import { Icon } from 'shared/ui/Icon'
import { useStore } from 'stores/useStore.ts'

import cls from './CalculationActionButton.module.scss'

const getMessagesWarnings = (
  key: MessagesWarnings,
  isEditRows: boolean,
  calcPossibility: ICalcPossibility | null,
  dataForStation?: ICalculationsPageStore['dataForStation'],
): ReactNode | null => {
  if (isEditRows) {
    return 'Включён режим редактирования'
  }
  switch (key) {
    case MessagesWarnings.UPDATE_VAULT:
      return 'Действия по станциям'
    case MessagesWarnings.DO_OPTIMIZATION:
      return calcPossibility?.warnings?.DO_OPTIMIZATION?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.DO_OPTIMIZATION.join('\n')}</div>
      ) : (
        'Запустить оптимизацию'
      )
    case MessagesWarnings.CALCULATE_ALLOWED_ZONES:
      return calcPossibility?.warnings?.CALCULATE_ALLOWED_ZONES?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.CALCULATE_ALLOWED_ZONES.join('\n')}</div>
      ) : (
        'Рассчитать допустимые зоны'
      )
    case MessagesWarnings.CALCULATE_GENERATION:
      return calcPossibility?.warnings?.CALCULATE_GENERATION?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.CALCULATE_GENERATION.join('\n')}</div>
      ) : (
        'Рассчитать плановую генерацию'
      )
    case MessagesWarnings.CALCULATE_GENERATION_MAXIMUM:
      return calcPossibility?.warnings?.CALCULATE_GENERATION_MAXIMUM?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.CALCULATE_GENERATION_MAXIMUM.join('\n')}</div>
      ) : (
        'Рассчитать генерацию по максимуму'
      )
    case MessagesWarnings.ENTERING_ALLOWED_ZONES:
      return calcPossibility?.warnings?.ENTERING_ALLOWED_ZONES?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.ENTERING_ALLOWED_ZONES.join('\n')}</div>
      ) : (
        'Рассчитать ввод в допустимые зоны относительно графика потребления'
      )
    case MessagesWarnings.ENTERING_ALLOWED_ZONES_TO_BOUNDS:
      return calcPossibility?.warnings?.ENTERING_ALLOWED_ZONES_TO_BOUNDS?.length > 0 ? (
        <div className={cls.TooltipMessage}>
          {calcPossibility?.warnings?.ENTERING_ALLOWED_ZONES_TO_BOUNDS.join('\n')}
        </div>
      ) : (
        'Рассчитать ввод в допустимые зоны в сторону ближайшей границы'
      )
    case MessagesWarnings.LOAD_ALL:
      return calcPossibility?.warnings?.LOAD_ALL?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.LOAD_ALL.join('\n')}</div>
      ) : (
        'Загрузить все данные'
      )
    case MessagesWarnings.LOAD_ISP:
      return calcPossibility?.warnings?.LOAD_ISP?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.LOAD_ISP.join('\n')}</div>
      ) : (
        'Загрузить график потребления'
      )
    case MessagesWarnings.LOAD_MODES:
      return calcPossibility?.warnings?.LOAD_MODES?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.LOAD_MODES.join('\n')}</div>
      ) : (
        'Загрузить данные из Модес'
      )
    case MessagesWarnings.LOAD_CM:
      return calcPossibility?.warnings?.LOAD_CM?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.LOAD_CM.join('\n')}</div>
      ) : (
        'Загрузить данные из РМ'
      )
    case MessagesWarnings.INITIALIZE:
      // Проверяем needInit из dataForStation
      if (dataForStation?.needInit === true) {
        return 'Исходные данные изменены, требуется переинициализация'
      }

      return calcPossibility?.warnings?.INITIALIZE?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.INITIALIZE.join('\n')}</div>
      ) : (
        'Выполнить инициализацию'
      )
    case MessagesWarnings.BALANCE_RGU:
      return calcPossibility?.warnings?.BALANCE_RGU?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.BALANCE_RGU.join('\n')}</div>
      ) : (
        'Распределить нагрузку по РГЕ'
      )
    case MessagesWarnings.WRITE_MODES:
      return calcPossibility?.warnings?.WRITE_MODES?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.WRITE_MODES.join('\n')}</div>
      ) : (
        'Запись плановых графиков в Модес'
      )
    case MessagesWarnings.SET_FIXING_VALUES:
      return calcPossibility?.warnings?.UPDATE_FIX?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.UPDATE_FIX.join('\n')}</div>
      ) : (
        'Установить фиксацию'
      )
    case MessagesWarnings.RESET_FIXING_VALUES:
      return calcPossibility?.warnings?.UPDATE_FIX?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.UPDATE_FIX.join('\n')}</div>
      ) : (
        'Снять фиксацию'
      )
    case MessagesWarnings.SEND_REPORT:
      return calcPossibility?.warnings?.SEND_REPORT?.length > 0 ? (
        <div className={cls.TooltipMessage}>{calcPossibility?.warnings?.SEND_REPORT.join('\n')}</div>
      ) : (
        'Рассылка отчетов'
      )
    case MessagesWarnings.HISTORY_ACCEPT:
      return 'История акцепта'
    case MessagesWarnings.HISTORY_MODES:
      return 'История записи в Модес'
    default:
      return null
  }
}

export interface CalculationActionButtonProps {
  buttonName: MessagesWarnings
  viewOnly: boolean
  isEditRows: boolean
  isLastDay: boolean
  editMode: boolean
  accepted?: boolean
  onClick: () => void
}

export interface CalculationActionButtonConfig {
  message: ReactNode | null
  disabled: boolean
  className: string
  variant: ButtonProps['variant']
  content: ReactNode
  loading: boolean
}

const getButtonConfigByKey = (
  key: MessagesWarnings,
  viewOnly: boolean,
  isEditRows: boolean,
  isLastDay: boolean,
  editMode: boolean,
  dataForStation: ICalculationsPageStore['dataForStation'],
  calcPossibility: ICalcPossibility | null,
  mode: 'vault' | 'station',
  statusVaultUpdateData: TStatusUpdateModalVault,
  isChangeFloodMode: boolean,
  isActualStage: boolean,
): CalculationActionButtonConfig => {
  let disabled = false
  const isStation = mode === 'station'
  const DONE_UPDATE = statusVaultUpdateData === 'DONE'
  const ERROR_UPDATE = statusVaultUpdateData === 'DONE_WITH_ERRORS'
  const PROGRESS_UPDATE = statusVaultUpdateData === 'IN_PROCESS'
  const NOT_EXECUTED_UPDATE = statusVaultUpdateData === 'NOT_EXECUTED'
  switch (key) {
    case MessagesWarnings.SET_FIXING_VALUES:
      disabled = isStation
        ? !calcPossibility?.canUpdateFix || viewOnly || !isActualStage || isLastDay || dataForStation.accepted
        : !calcPossibility?.canUpdateFix || viewOnly || !isActualStage || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.SET_FIXING_VALUES, false, calcPossibility, dataForStation),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='closeLock' />,
        loading: false,
      }
    case MessagesWarnings.RESET_FIXING_VALUES:
      disabled = isStation
        ? !calcPossibility?.canUpdateFix || viewOnly || !isActualStage || isLastDay || dataForStation.accepted
        : !calcPossibility?.canUpdateFix || viewOnly || !isActualStage || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.RESET_FIXING_VALUES, false, calcPossibility, dataForStation),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='openLock' />,
        loading: false,
      }
    case MessagesWarnings.UPDATE_VAULT:
      disabled = isEditRows

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.UPDATE_VAULT, isEditRows, calcPossibility, dataForStation),
        className: classNames(
          cls.ButtonUpdateVault,
          {
            [cls.DisabledButton]: disabled,
            [cls.PROGRESS_UPDATE]: PROGRESS_UPDATE,
            [cls.DONE_UPDATE]: DONE_UPDATE,
            [cls.ERROR_UPDATE]: ERROR_UPDATE,
            [cls.NOT_EXECUTED_UPDATE]: NOT_EXECUTED_UPDATE,
          },
          [],
        ),
        content: <Icon width={20} name='update' />,
        loading: false,
      }
    case MessagesWarnings.DO_OPTIMIZATION:
      disabled = isStation
        ? !calcPossibility?.canDoOptimization ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : !calcPossibility?.canDoOptimization || viewOnly || !isActualStage || isEditRows || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.DO_OPTIMIZATION, isEditRows, calcPossibility, dataForStation),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='star' />,
        loading: calcPossibility?.taskStatus[MessagesWarnings.DO_OPTIMIZATION] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.CALCULATE_ALLOWED_ZONES:
      disabled = isStation
        ? !calcPossibility?.canCalculateAllowedZones ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : !calcPossibility?.canCalculateAllowedZones || viewOnly || !isActualStage || isEditRows || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(
          MessagesWarnings.CALCULATE_ALLOWED_ZONES,
          isEditRows,
          calcPossibility,
          dataForStation,
        ),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='calculationOfPermissibleZones' />,
        loading: calcPossibility?.taskStatus[MessagesWarnings.CALCULATE_ALLOWED_ZONES] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.CALCULATE_GENERATION:
      disabled = isStation
        ? !calcPossibility?.canCalculateGeneration ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : !calcPossibility?.canCalculateGeneration || viewOnly || !isActualStage || isEditRows || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(
          MessagesWarnings.CALCULATE_GENERATION,
          isEditRows,
          calcPossibility,
          dataForStation,
        ),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='calculationOfThePlannedGenerationSchedule' />,
        loading: calcPossibility?.taskStatus[MessagesWarnings.CALCULATE_GENERATION] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.CALCULATE_GENERATION_MAXIMUM:
      disabled = isStation
        ? !calcPossibility?.canCalculateGenerationMaximum ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : !calcPossibility?.canCalculateGenerationMaximum || viewOnly || !isActualStage || isEditRows || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(
          MessagesWarnings.CALCULATE_GENERATION_MAXIMUM,
          isEditRows,
          calcPossibility,
          dataForStation,
        ),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='max' />,
        loading: calcPossibility?.taskStatus[MessagesWarnings.CALCULATE_GENERATION] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.ENTERING_ALLOWED_ZONES:
      disabled = isStation
        ? !calcPossibility?.canEnteringAllowedZones ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : !calcPossibility?.canEnteringAllowedZones || viewOnly || !isActualStage || isEditRows || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(
          MessagesWarnings.ENTERING_ALLOWED_ZONES,
          isEditRows,
          calcPossibility,
          dataForStation,
        ),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='enteringIntoAnAcceptableAreaRelativeToTheConsumptionSchedule' />,
        loading: calcPossibility?.taskStatus[MessagesWarnings.ENTERING_ALLOWED_ZONES] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.ENTERING_ALLOWED_ZONES_TO_BOUNDS:
      disabled = isStation
        ? !calcPossibility?.canEnteringAllowedZonesToBounds ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : !calcPossibility?.canEnteringAllowedZonesToBounds || viewOnly || !isActualStage || isEditRows || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(
          MessagesWarnings.ENTERING_ALLOWED_ZONES_TO_BOUNDS,
          isEditRows,
          calcPossibility,
          dataForStation,
        ),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='enteringIntoAnAcceptableAreaRelativeToTheNearestBorder' />,
        loading:
          calcPossibility?.taskStatus[MessagesWarnings.ENTERING_ALLOWED_ZONES_TO_BOUNDS] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.LOAD_ALL:
      disabled = isStation
        ? !calcPossibility?.canLoadAll ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : !calcPossibility?.canLoadAll || viewOnly || !isActualStage || isEditRows || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.LOAD_ALL, isEditRows, calcPossibility, dataForStation),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='loadData' />,
        loading:
          calcPossibility?.taskStatus[MessagesWarnings.LOAD_CM] === TaskStatus.IN_PROCESS ||
          calcPossibility?.taskStatus[MessagesWarnings.LOAD_ISP] === TaskStatus.IN_PROCESS ||
          calcPossibility?.taskStatus[MessagesWarnings.LOAD_MODES] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.LOAD_ISP:
      disabled = isStation
        ? !calcPossibility?.canLoadIsp ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : !calcPossibility?.canLoadIsp || viewOnly || !isActualStage || isEditRows || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.LOAD_ISP, isEditRows, calcPossibility, dataForStation),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='isp' />,
        loading: calcPossibility?.taskStatus[MessagesWarnings.LOAD_ISP] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.LOAD_MODES:
      disabled = isStation
        ? !calcPossibility?.canLoadModes ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : !calcPossibility?.canLoadModes || viewOnly || !isActualStage || isEditRows || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.LOAD_MODES, isEditRows, calcPossibility, dataForStation),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='modes' />,
        loading: calcPossibility?.taskStatus[MessagesWarnings.LOAD_MODES] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.LOAD_CM:
      disabled = isStation
        ? !calcPossibility?.canLoadCm ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : !calcPossibility?.canLoadCm || viewOnly || !isActualStage || isEditRows || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.LOAD_CM, isEditRows, calcPossibility, dataForStation),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='rm' />,
        loading: calcPossibility?.taskStatus[MessagesWarnings.LOAD_CM] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.BALANCE_RGU:
      disabled = isStation
        ? !calcPossibility?.canBalanceRgu ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : !calcPossibility?.canBalanceRgu || viewOnly || !isActualStage || isEditRows || isLastDay

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.BALANCE_RGU, isEditRows, calcPossibility, dataForStation),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='calcRGE' />,
        loading: calcPossibility?.taskStatus[MessagesWarnings.BALANCE_RGU] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.WRITE_MODES:
      disabled = isStation ? !calcPossibility?.canWriteModes || isLastDay || isEditRows : isLastDay || isEditRows

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.WRITE_MODES, isEditRows, calcPossibility, dataForStation),
        className: classNames(cls.ButtonHistoryForRegular, { [cls.DisabledButton]: disabled }, []),
        content: <Icon height={20} name='modesWrite' />,
        loading: calcPossibility?.taskStatus[MessagesWarnings.WRITE_MODES] === TaskStatus.IN_PROCESS,
      }
    case MessagesWarnings.RESET:
      disabled = viewOnly || !isActualStage || !isEditRows || isLastDay || dataForStation.accepted

      return {
        variant: 'outlined',
        disabled: disabled,
        message: null,
        className: cls.ButtonReset,
        content: 'Сбросить',
        loading: false,
      }
    case MessagesWarnings.RESET_VAULT:
      disabled = (!isChangeFloodMode && !isEditRows) || viewOnly || !isActualStage

      return {
        variant: 'outlined',
        disabled: disabled,
        message: null,
        className: cls.ButtonReset,
        content: 'Сбросить',
        loading: false,
      }
    case MessagesWarnings.INITIALIZE: {
      disabled = isStation
        ? !calcPossibility?.canInitialize ||
          !editMode ||
          viewOnly ||
          !isActualStage ||
          isEditRows ||
          isLastDay ||
          dataForStation.accepted
        : false

      // Определяем класс кнопки в зависимости от needInit
      const initButtonClass = dataForStation?.needInit === true ? cls.ButtonInitNeedInit : cls.ButtonInit

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.INITIALIZE, isEditRows, calcPossibility, dataForStation),
        className: initButtonClass,
        content: <Icon width={20} name='clear' />,
        loading: false,
      }
    }
    case MessagesWarnings.HISTORY_ACCEPT:
      return {
        variant: 'text',
        disabled: isEditRows,
        message: getMessagesWarnings(MessagesWarnings.HISTORY_ACCEPT, isEditRows, calcPossibility, dataForStation),
        className: cls.ButtonHistoryForRegular,
        content: <Icon width={20} name='history' />,
        loading: false,
      }
    case MessagesWarnings.HISTORY_MODES:
      return {
        variant: 'text',
        disabled: isEditRows,
        message: getMessagesWarnings(MessagesWarnings.HISTORY_MODES, isEditRows, calcPossibility, dataForStation),
        className: cls.ButtonHistoryForRegular,
        content: <Icon width={20} name='history' />,
        loading: false,
      }
    case MessagesWarnings.SEND_REPORT:
      disabled = !calcPossibility?.canSendReport || isEditRows

      return {
        variant: 'text',
        disabled: disabled,
        message: getMessagesWarnings(MessagesWarnings.SEND_REPORT, isEditRows, calcPossibility, dataForStation),
        className: classNames(cls.ButtonIcon, { [cls.DisabledButton]: disabled }, []),
        content: <Icon width={20} name='newsletter' />,
        loading: false,
      }
    default:
      return {
        message: null,
        disabled: false,
        className: '',
        variant: 'text',
        content: <></>,
        loading: false,
      }
  }
}

export const CalculationActionButton: FC<CalculationActionButtonProps> = observer((props) => {
  const { buttonName, viewOnly, isEditRows, isLastDay, editMode, onClick } = props
  const { calculationsPageStore } = useStore()
  const { dataForStation, calcPossibility, selectLeftMenu, vaultStore, isActualStage } = calculationsPageStore
  const { statusVaultUpdateData, isChangeFloodMode } = vaultStore
  const mode = selectLeftMenu === 0 ? 'vault' : 'station'
  const buttonConfig: CalculationActionButtonConfig = useMemo(() => {
    const condition =
      mode === 'station'
        ? calcPossibility || buttonName === MessagesWarnings.SAVE_VAULT || buttonName === MessagesWarnings.RESET_VAULT
        : true
    if (condition) {
      return getButtonConfigByKey(
        buttonName,
        viewOnly,
        isEditRows,
        isLastDay,
        editMode,
        dataForStation,
        calcPossibility,
        mode,
        statusVaultUpdateData,
        isChangeFloodMode,
        isActualStage,
      )
    }

    return {
      message: null,
      disabled: true,
      className: '',
      variant: 'text',
      content: '',
      loading: false,
    }
  }, [
    buttonName,
    calcPossibility,
    viewOnly,
    isEditRows,
    isLastDay,
    editMode,
    dataForStation,
    statusVaultUpdateData,
    isChangeFloodMode,
  ])

  if (buttonConfig.loading) {
    return (
      <div className={classNames(cls.LoaderContainer, {}, [buttonConfig.className])}>
        <CircularProgress style={{ width: 10, height: 10 }} />
      </div>
    )
  }

  return (
    <div className={cls.ButtonContainer}>
      <Button
        message={buttonConfig.message}
        onClick={onClick}
        className={buttonConfig.className}
        variant={buttonConfig.variant}
        disabled={buttonConfig.disabled}
        loading={buttonConfig.loading}
      >
        {buttonConfig.content}
      </Button>
    </div>
  )
})
